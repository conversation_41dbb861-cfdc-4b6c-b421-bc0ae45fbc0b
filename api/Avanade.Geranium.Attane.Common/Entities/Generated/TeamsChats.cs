/*
 * This file is automatically generated; any changes will be lost. 
 */

#nullable enable
#pragma warning disable

using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Text.Json.Serialization;
using CoreEx.Entities;

namespace Avanade.Geranium.Attane.Common.Entities
{
    /// <summary>
    /// Represents the Teams チャット設定 entity.
    /// </summary>
    public partial class TeamsChats
    {
        /// <summary>
        /// Gets or sets the ID.
        /// </summary>
        public string? Id { get; set; }

        /// <summary>
        /// Gets or sets the チャットID.
        /// </summary>
        public string? ChatId { get; set; }

        /// <summary>
        /// Gets or sets the チャネルID.
        /// </summary>
        public string? ChannelId { get; set; }

        /// <summary>
        /// Gets or sets the チャットタイプ.
        /// </summary>
        public string? ChatType { get; set; }

        /// <summary>
        /// Gets or sets the 名前.
        /// </summary>
        public string? Name { get; set; }

        /// <summary>
        /// Gets or sets the ユーザーID.
        /// </summary>
        public string? UserId { get; set; }

        /// <summary>
        /// Gets or sets the 作成日時.
        /// </summary>
        public DateTime? CreatedAt { get; set; }

        /// <summary>
        /// Gets or sets the 更新日時.
        /// </summary>
        public DateTime? UpdatedAt { get; set; }
    }

    /// <summary>
    /// Represents the <see cref="TeamsChats"/> collection.
    /// </summary>
    public partial class TeamsChatsCollection : List<TeamsChats> { }
}

#pragma warning restore
#nullable restore
