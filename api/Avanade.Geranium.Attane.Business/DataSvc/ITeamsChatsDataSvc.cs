using Avanade.Geranium.Attane.Business.Entities;

namespace Avanade.Geranium.Attane.Business.DataSvc
{
    /// <summary>
    /// Provides the <see cref="TeamsChats"/> data access functionality.
    /// </summary>
    public interface ITeamsChatsDataSvc
    {
        /// <summary>
        /// Creates a new Teams チャット設定.
        /// </summary>
        /// <param name="value">The <see cref="TeamsChats"/> to create.</param>
        /// <returns>The created <see cref="TeamsChats"/>.</returns>
        Task<TeamsChats> CreateAsync(TeamsChats value);

        /// <summary>
        /// Gets Teams チャット設定 by ID.
        /// </summary>
        /// <param name="id">The Teams チャット設定 ID.</param>
        /// <returns>The <see cref="TeamsChats"/> if found; otherwise, null.</returns>
        Task<TeamsChats?> GetByIdAsync(string id);

        /// <summary>
        /// Gets all Teams チャット設定 for the specified user.
        /// </summary>
        /// <param name="userId">The user ID.</param>
        /// <returns>The collection of <see cref="TeamsChats"/>.</returns>
        Task<IEnumerable<TeamsChats>?> GetByUserIdAsync(string userId);

        /// <summary>
        /// Deletes the Teams チャット設定.
        /// </summary>
        /// <param name="id">The Teams チャット設定 ID.</param>
        /// <returns>A task that represents the asynchronous operation.</returns>
        Task DeleteAsync(string id);
    }
}
