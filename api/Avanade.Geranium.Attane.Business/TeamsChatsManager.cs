using CoreEx.Entities;
using Avanade.Geranium.Attane.Business.Entities;

namespace Avanade.Geranium.Attane.Business
{
    /// <summary>
    /// Provides the <see cref="TeamsChats"/> business functionality implementation.
    /// </summary>
    public partial class TeamsChatsManager
    {
        /// <summary>
        /// Teams チャット設定を作成します.
        /// </summary>
        /// <param name="value">The <see cref="TeamsChats"/>.</param>
        /// <param name="userId">The 対象のユーザーID.</param>
        /// <returns>The created <see cref="TeamsChats"/>.</returns>
        /// <remarks>
        /// <see cref="TeamsChatsManager.CreateAsync(TeamsChats, string)"/>の実装
        /// </remarks>
        public async Task<TeamsChats> CreateOnImplementationAsync(TeamsChats value, string userId)
        {
            // バリデーション
            if (value == null)
                throw new ArgumentNullException(nameof(value));
            
            if (string.IsNullOrWhiteSpace(userId))
                throw new ArgumentException("UserId is required", nameof(userId));

            if (string.IsNullOrWhiteSpace(value.Name))
                throw new ArgumentException("Name is required");

            if (string.IsNullOrWhiteSpace(value.ChatType))
                throw new ArgumentException("ChatType is required");

            // チャットタイプに応じた必須項目チェック
            if (value.ChatType == "チャット" && string.IsNullOrWhiteSpace(value.ChatId))
                throw new ArgumentException("ChatId is required for chat type");

            if (value.ChatType == "チャネル" && string.IsNullOrWhiteSpace(value.ChannelId))
                throw new ArgumentException("ChannelId is required for channel type");

            // IDと日時を設定
            value.Id = Guid.NewGuid().ToString();
            value.UserId = userId;
            value.CreatedAt = DateTime.UtcNow;
            value.UpdatedAt = DateTime.UtcNow;

            // データベースに保存
            return await _dataService.CreateAsync(value).ConfigureAwait(false);
        }

        /// <summary>
        /// 指定されたユーザーのTeams チャット設定を取得します.
        /// </summary>
        /// <param name="userId">The 対象のユーザーID.</param>
        /// <returns>The <see cref="TeamsChatsCollection"/>.</returns>
        /// <remarks>
        /// <see cref="TeamsChatsManager.GetByUserIdAsync(string)"/>の実装
        /// </remarks>
        public async Task<TeamsChatsCollection> GetByUserIdOnImplementationAsync(string userId)
        {
            if (string.IsNullOrWhiteSpace(userId))
                throw new ArgumentException("UserId is required", nameof(userId));

            var result = await _dataService.GetByUserIdAsync(userId).ConfigureAwait(false);
            return new TeamsChatsCollection(result ?? Enumerable.Empty<TeamsChats>());
        }

        /// <summary>
        /// Teams チャット設定を削除します.
        /// </summary>
        /// <param name="id">The Teams チャット設定 ID.</param>
        /// <param name="userId">The 対象のユーザーID.</param>
        /// <returns>A task that represents the asynchronous operation.</returns>
        /// <remarks>
        /// <see cref="TeamsChatsManager.DeleteAsync(string, string)"/>の実装
        /// </remarks>
        public async Task DeleteOnImplementationAsync(string id, string userId)
        {
            if (string.IsNullOrWhiteSpace(id))
                throw new ArgumentException("Id is required", nameof(id));

            if (string.IsNullOrWhiteSpace(userId))
                throw new ArgumentException("UserId is required", nameof(userId));

            // 指定されたユーザーのデータかどうかを確認してから削除
            var existing = await _dataService.GetByIdAsync(id).ConfigureAwait(false);
            if (existing == null)
                throw new NotFoundException($"TeamsChats with id '{id}' not found");

            if (existing.UserId != userId)
                throw new AuthorizationException($"User '{userId}' is not authorized to delete this TeamsChats");

            await _dataService.DeleteAsync(id).ConfigureAwait(false);
        }
    }
}
