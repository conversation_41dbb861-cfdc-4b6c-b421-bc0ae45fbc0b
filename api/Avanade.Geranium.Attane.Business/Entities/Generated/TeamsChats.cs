/*
 * This file is automatically generated; any changes will be lost. 
 */

#nullable enable
#pragma warning disable

using CoreEx.Entities;

namespace Avanade.Geranium.Attane.Business.Entities
{
    /// <summary>
    /// Represents the Teams チャット設定 entity.
    /// </summary>
    public partial class TeamsChats : EntityBase, IPrimaryKey
    {
        private string? _id;
        private string? _chatId;
        private string? _channelId;
        private string? _chatType;
        private string? _name;
        private string? _userId;
        private DateTime? _createdAt;
        private DateTime? _updatedAt;

        /// <summary>
        /// Gets or sets the ID.
        /// </summary>
        public string? Id { get => _id; set => SetValue(ref _id, value); }

        /// <summary>
        /// Gets or sets the チャットID.
        /// </summary>
        public string? ChatId { get => _chatId; set => SetValue(ref _chatId, value); }

        /// <summary>
        /// Gets or sets the チャネルID.
        /// </summary>
        public string? ChannelId { get => _channelId; set => SetValue(ref _channelId, value); }

        /// <summary>
        /// Gets or sets the チャットタイプ.
        /// </summary>
        public string? ChatType { get => _chatType; set => SetValue(ref _chatType, value); }

        /// <summary>
        /// Gets or sets the 名前.
        /// </summary>
        public string? Name { get => _name; set => SetValue(ref _name, value); }

        /// <summary>
        /// Gets or sets the ユーザーID.
        /// </summary>
        public string? UserId { get => _userId; set => SetValue(ref _userId, value); }

        /// <summary>
        /// Gets or sets the 作成日時.
        /// </summary>
        public DateTime? CreatedAt { get => _createdAt; set => SetValue(ref _createdAt, value); }

        /// <summary>
        /// Gets or sets the 更新日時.
        /// </summary>
        public DateTime? UpdatedAt { get => _updatedAt; set => SetValue(ref _updatedAt, value); }

        /// <inheritdoc/>
        protected override IEnumerable<IPropertyValue> GetPropertyValues()
        {
            yield return CreateProperty(nameof(Id), Id, v => Id = v);
            yield return CreateProperty(nameof(ChatId), ChatId, v => ChatId = v);
            yield return CreateProperty(nameof(ChannelId), ChannelId, v => ChannelId = v);
            yield return CreateProperty(nameof(ChatType), ChatType, v => ChatType = v);
            yield return CreateProperty(nameof(Name), Name, v => Name = v);
            yield return CreateProperty(nameof(UserId), UserId, v => UserId = v);
            yield return CreateProperty(nameof(CreatedAt), CreatedAt, v => CreatedAt = v);
            yield return CreateProperty(nameof(UpdatedAt), UpdatedAt, v => UpdatedAt = v);
        }

        /// <inheritdoc/>
        public object[] PrimaryKey => new object[] { Id! };
    }

    /// <summary>
    /// Represents the <see cref="TeamsChats"/> collection.
    /// </summary>
    public partial class TeamsChatsCollection : EntityBaseCollection<TeamsChats, TeamsChatsCollection>
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="TeamsChatsCollection"/> class.
        /// </summary>
        public TeamsChatsCollection() { }

        /// <summary>
        /// Initializes a new instance of the <see cref="TeamsChatsCollection"/> class with <paramref name="items"/> to add.
        /// </summary>
        /// <param name="items">The items to add.</param>
        public TeamsChatsCollection(IEnumerable<TeamsChats> items) => AddRange(items);
    }
}

#pragma warning restore
#nullable restore
