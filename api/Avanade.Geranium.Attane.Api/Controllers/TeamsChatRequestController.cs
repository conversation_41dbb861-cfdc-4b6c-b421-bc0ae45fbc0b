using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Net;
using CoreEx.WebApis;
using Microsoft.Extensions.Logging;
using Avanade.Geranium.Attane.Business;
using Avanade.Geranium.Attane.Business.Entities;

namespace Avanade.Geranium.Attane.Api.Controllers
{
    /// <summary>
    /// Provides the Teams Chat Request Web API functionality.
    /// </summary>
    [Authorize]
    [Route("users")]
    [Produces(System.Net.Mime.MediaTypeNames.Application.Json)]
    public partial class TeamsChatRequestController : ControllerBase
    {
        private readonly WebApi _webApi;
        private readonly ILogger<TeamsChatRequestController> _logger;
        private readonly Caller.ICallerProvider _callerProvider;
        private readonly ITeamsChatsManager _teamsChatsManager;

        /// <summary>
        /// Initializes a new instance of the <see cref="TeamsChatRequestController"/> class.
        /// </summary>
        /// <param name="webApi">The <see cref="WebApi"/>.</param>
        /// <param name="logger">The <see cref="ILogger{TeamsChatRequestController}"/>.</param>
        /// <param name="callerProvider">The <see cref="Caller.ICallerProvider"/>.</param>
        /// <param name="teamsChatsManager">The <see cref="ITeamsChatsManager"/>.</param>
        public TeamsChatRequestController(
            WebApi webApi,
            ILogger<TeamsChatRequestController> logger,
            Caller.ICallerProvider callerProvider,
            ITeamsChatsManager teamsChatsManager)
        {
            _webApi = webApi ?? throw new ArgumentNullException(nameof(webApi));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _callerProvider = callerProvider ?? throw new ArgumentNullException(nameof(callerProvider));
            _teamsChatsManager = teamsChatsManager ?? throw new ArgumentNullException(nameof(teamsChatsManager));
        }

        /// <summary>
        /// Teams チャット設定を登録します.
        /// </summary>
        /// <param name="userId">The 対象のユーザーID.</param>
        /// <returns>The created Teams chat configuration.</returns>
        [HttpPost("{userId}/teams-chats")]
        [ProducesResponseType((int)HttpStatusCode.Created)]
        public Task<IActionResult> RegisterTeamsChat(string userId) =>
            _logger.LogBlockAsync(() =>
            {
                _callerProvider.ValidateUserId(this, _logger, userId);
                return _webApi.PostAsync<TeamsChatsRequest, TeamsChatsResponse>(Request, async request =>
                {
                    var teamsChats = new TeamsChats
                    {
                        ChatId = request.Value?.ChatId,
                        ChannelId = request.Value?.ChannelId,
                        ChatType = request.Value?.ChatType,
                        Name = request.Value?.Name
                    };

                    var created = await _teamsChatsManager.CreateAsync(teamsChats, userId);

                    var response = new TeamsChatsResponse
                    {
                        Id = created.Id ?? string.Empty,
                        ChatId = created.ChatId,
                        ChannelId = created.ChannelId,
                        ChatType = created.ChatType ?? string.Empty,
                        Name = created.Name ?? string.Empty,
                        UserId = created.UserId ?? string.Empty,
                        CreatedAt = created.CreatedAt?.ToString("yyyy-MM-ddTHH:mm:ss.fffZ") ?? string.Empty,
                        UpdatedAt = created.UpdatedAt?.ToString("yyyy-MM-ddTHH:mm:ss.fffZ") ?? string.Empty
                    };

                    return response;
                }, statusCode: HttpStatusCode.Created);
            }, nameof(RegisterTeamsChat), $", userId: {userId}");

        /// <summary>
        /// Teams チャット設定を取得します.
        /// </summary>
        /// <param name="userId">The 対象のユーザーID.</param>
        /// <returns>The Teams chat configurations.</returns>
        [HttpGet("{userId}/teams-chats")]
        [ProducesResponseType(typeof(TeamsChatsResponse[]), (int)HttpStatusCode.OK)]
        [ProducesResponseType((int)HttpStatusCode.NoContent)]
        public Task<IActionResult> GetTeamsChats(string userId) =>
            _logger.LogBlockAsync(async () =>
            {
                _callerProvider.ValidateUserId(this, _logger, userId);

                var teamsChatsCollection = await _teamsChatsManager.GetByUserIdAsync(userId);

                if (teamsChatsCollection == null || !teamsChatsCollection.Any())
                {
                    return NoContent();
                }

                var responses = teamsChatsCollection.Select(tc => new TeamsChatsResponse
                {
                    Id = tc.Id ?? string.Empty,
                    ChatId = tc.ChatId,
                    ChannelId = tc.ChannelId,
                    ChatType = tc.ChatType ?? string.Empty,
                    Name = tc.Name ?? string.Empty,
                    UserId = tc.UserId ?? string.Empty,
                    CreatedAt = tc.CreatedAt?.ToString("yyyy-MM-ddTHH:mm:ss.fffZ") ?? string.Empty,
                    UpdatedAt = tc.UpdatedAt?.ToString("yyyy-MM-ddTHH:mm:ss.fffZ") ?? string.Empty
                }).ToArray();

                return Ok(responses);
            }, nameof(GetTeamsChats), $", userId: {userId}");

        /// <summary>
        /// Teams チャット設定を削除します.
        /// </summary>
        /// <param name="userId">The 対象のユーザーID.</param>
        /// <param name="id">The Teams chat configuration ID.</param>
        /// <returns>No content.</returns>
        [HttpDelete("{userId}/teams-chats/{id}")]
        [ProducesResponseType((int)HttpStatusCode.NoContent)]
        public Task<IActionResult> DeleteTeamsChat(string userId, string id) =>
            _logger.LogBlockAsync(async () =>
            {
                _callerProvider.ValidateUserId(this, _logger, userId);

                await _teamsChatsManager.DeleteAsync(id, userId);
                return NoContent();
            }, nameof(DeleteTeamsChat), $", userId: {userId}, id: {id}");
    }

    /// <summary>
    /// Teams チャット設定のリクエスト.
    /// </summary>
    public class TeamsChatsRequest
    {
        /// <summary>
        /// Gets or sets the チャットID.
        /// </summary>
        public string? ChatId { get; set; }

        /// <summary>
        /// Gets or sets the チャネルID.
        /// </summary>
        public string? ChannelId { get; set; }

        /// <summary>
        /// Gets or sets the チャットタイプ.
        /// </summary>
        public string? ChatType { get; set; }

        /// <summary>
        /// Gets or sets the 名前.
        /// </summary>
        public string? Name { get; set; }
    }

    /// <summary>
    /// Teams チャット設定のレスポンス.
    /// </summary>
    public class TeamsChatsResponse
    {
        /// <summary>
        /// Gets or sets the ID.
        /// </summary>
        public string Id { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the チャットID.
        /// </summary>
        public string? ChatId { get; set; }

        /// <summary>
        /// Gets or sets the チャネルID.
        /// </summary>
        public string? ChannelId { get; set; }

        /// <summary>
        /// Gets or sets the チャットタイプ.
        /// </summary>
        public string ChatType { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the 名前.
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the ユーザーID.
        /// </summary>
        public string UserId { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the 作成日時.
        /// </summary>
        public string CreatedAt { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the 更新日時.
        /// </summary>
        public string UpdatedAt { get; set; } = string.Empty;
    }
}