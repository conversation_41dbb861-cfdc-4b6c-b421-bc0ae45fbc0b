using Azure.Data.Tables;
using Avanade.Geranium.Attane.Business.DataSvc;
using Avanade.Geranium.Attane.Business.Entities;
using Avanade.Geranium.Attane.Infrastructure.Data.Clients;

namespace Avanade.Geranium.Attane.Infrastructure.Data
{
    /// <summary>
    /// Provides the <see cref="TeamsChats"/> data access functionality using Table Storage.
    /// </summary>
    public class TeamsChatsDataSvc : ITeamsChatsDataSvc
    {
        private readonly ITableStorageClient _tableStorageClient;
        private const string TableName = "TeamsChats";

        /// <summary>
        /// Initializes a new instance of the <see cref="TeamsChatsDataSvc"/> class.
        /// </summary>
        /// <param name="tableStorageClient">The <see cref="ITableStorageClient"/>.</param>
        public TeamsChatsDataSvc(ITableStorageClient tableStorageClient)
        {
            _tableStorageClient = tableStorageClient ?? throw new ArgumentNullException(nameof(tableStorageClient));
        }

        /// <summary>
        /// Creates a new Teams チャット設定.
        /// </summary>
        /// <param name="value">The <see cref="TeamsChats"/> to create.</param>
        /// <returns>The created <see cref="TeamsChats"/>.</returns>
        public async Task<TeamsChats> CreateAsync(TeamsChats value)
        {
            if (value == null)
                throw new ArgumentNullException(nameof(value));

            var tableEntity = TeamsChatsTableEntity.FromTeamsChats(value);
            var operation = new TableOperation<TeamsChatsTableEntity>(TableTransactionActionType.Add, tableEntity);
            
            var result = await _tableStorageClient.ExecuteTableOperation(TableName, operation);
            
            if (result == null)
                throw new InvalidOperationException("Failed to create TeamsChats");

            return result.ToTeamsChats();
        }

        /// <summary>
        /// Gets Teams チャット設定 by ID.
        /// </summary>
        /// <param name="id">The Teams チャット設定 ID.</param>
        /// <returns>The <see cref="TeamsChats"/> if found; otherwise, null.</returns>
        public async Task<TeamsChats?> GetByIdAsync(string id)
        {
            if (string.IsNullOrWhiteSpace(id))
                throw new ArgumentException("Id is required", nameof(id));

            // IDだけでは検索できないため、全件取得してフィルタリング
            // 実際の運用では、より効率的な検索方法を検討する必要があります
            var filter = $"RowKey eq '{id}'";
            var results = await _tableStorageClient.Get<TeamsChatsTableEntity>(TableName, filter);
            
            var entity = results.FirstOrDefault();
            return entity?.ToTeamsChats();
        }

        /// <summary>
        /// Gets all Teams チャット設定 for the specified user.
        /// </summary>
        /// <param name="userId">The user ID.</param>
        /// <returns>The collection of <see cref="TeamsChats"/>.</returns>
        public async Task<IEnumerable<TeamsChats>?> GetByUserIdAsync(string userId)
        {
            if (string.IsNullOrWhiteSpace(userId))
                throw new ArgumentException("UserId is required", nameof(userId));

            var filter = $"PartitionKey eq '{userId}'";
            var results = await _tableStorageClient.Get<TeamsChatsTableEntity>(TableName, filter);
            
            return results.Select(entity => entity.ToTeamsChats()).ToList();
        }

        /// <summary>
        /// Deletes the Teams チャット設定.
        /// </summary>
        /// <param name="id">The Teams チャット設定 ID.</param>
        /// <returns>A task that represents the asynchronous operation.</returns>
        public async Task DeleteAsync(string id)
        {
            if (string.IsNullOrWhiteSpace(id))
                throw new ArgumentException("Id is required", nameof(id));

            // まず対象のエンティティを取得
            var existing = await GetByIdAsync(id);
            if (existing == null)
                throw new InvalidOperationException($"TeamsChats with id '{id}' not found");

            var tableEntity = TeamsChatsTableEntity.FromTeamsChats(existing);
            await _tableStorageClient.Delete(TableName, tableEntity);
        }
    }
}
