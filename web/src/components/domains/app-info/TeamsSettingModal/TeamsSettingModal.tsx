import * as React from 'react';
import {
  <PERSON><PERSON>, Header, Input,
} from '@fluentui/react-northstar';
import { AddIcon, AcceptIcon, CloseIcon } from '@fluentui/react-icons-northstar';
import { mergedClassName } from '../../../../utilities/commonFunction';
import ModalCardTop from '../../../commons/molecules/modal-card-top/ModalCardTop';
import useUserChatsAndChannelsAccessor, { IUserChatItem } from '../../../../hooks/accessors/useUserChatsAndChannelsAccessor';
import useComponentInitUtility from '../../../../hooks/utilities/useComponentInitUtility';

// CSS
import './TeamsSettingModal.scss';

// チャットアイテムの型定義
export interface IChatItem {
  id: string;
  name: string;
  type: 'チャット' | 'チャネル';
  isBookmarked: boolean;
}

export interface ISimpleModalProps {
  className?: string;
  open?: boolean;
  title?: string;
  onClose?: () => void;
  // userId?: string; // ユーザーIDの登録も必要かも
  chatItems?: IChatItem[];
}

/**
 * TeamsSettingModal
 * @param props
 */
const TeamsSettingModal: React.FC<ISimpleModalProps> = (props) => {
  const {
    className,
    open,
    title = 'Teams設定',
    onClose,
    chatItems: propsChatItems,
  } = props;

  // コンポーネント初期化ユーティリティ
  const [, , callbacks] = useComponentInitUtility({
    componentName: 'TeamsSettingModal',
  });
  const tokenProvider = React.useMemo(() => {
    if (!callbacks?.get) return undefined;
    const graphTokenProvider = callbacks.get('graph');
    return graphTokenProvider ? () => graphTokenProvider() : undefined;
  }, [callbacks]);

  // APIアクセサーを初期化
  const {
    fetchUserChatsAndChannels,
    isLoading, error,
  } = useUserChatsAndChannelsAccessor(tokenProvider);

  // 状態管理
  const [chatId, setChatId] = React.useState('');
  const [allChatItems, setAllChatItems] = React.useState<IUserChatItem[]>([]);
  const [filteredChatItems, setFilteredChatItems] = React.useState<IUserChatItem[]>([]);
  const [selectedItems, setSelectedItems] = React.useState<Set<string>>(new Set());

  // マージされたCSSクラス名
  const rootClassName = React.useMemo(() => {
    const step1 = mergedClassName('simple-modal', className);
    const isOpen = open ? 'is-open' : '';
    return mergedClassName(isOpen, step1);
  }, [className, open]);

  // データ取得のEffect
  React.useEffect(() => {
    if (open && fetchUserChatsAndChannels) {
      fetchUserChatsAndChannels()
        .then((items) => {
          setAllChatItems(items);
          setFilteredChatItems(items);
        })
        .catch((err) => {
          // エラーが発生した場合はプロパティで渡されたアイテムを使用
          if (propsChatItems) {
            const convertedItems: IUserChatItem[] = propsChatItems.map((item) => ({
              ...item,
            }));
            setAllChatItems(convertedItems);
            setFilteredChatItems(convertedItems);
          }
          // エラーを再スローして上位でハンドリングできるようにする
          throw err;
        });
    }
  }, [open, fetchUserChatsAndChannels, propsChatItems]);

  // 検索フィルタリングのEffect
  React.useEffect(() => {
    if (!chatId.trim()) {
      setFilteredChatItems(allChatItems);
    } else {
      const filtered = allChatItems.filter(
        (item) => item.id.toLowerCase().includes(chatId.toLowerCase())
        || item.name.toLowerCase().includes(chatId.toLowerCase()),
      );
      setFilteredChatItems(filtered);
    }
  }, [chatId, allChatItems]);

  const handleClose = React.useCallback(() => {
    if (onClose) onClose();
  }, [onClose]);

  // チャットID入力の変更ハンドラー
  const handleChatIdChange = React.useCallback(
    (_e: React.SyntheticEvent<HTMLElement>, data?: { value?: string }) => {
      setChatId(data?.value ?? '');
    },
    [],
  );

  // アイテム選択切り替えハンドラー
  const handleItemToggle = React.useCallback((itemId: string) => {
    setSelectedItems((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(itemId)) {
        newSet.delete(itemId);
      } else {
        newSet.add(itemId);
      }
      return newSet;
    });
  }, []);

  // キーボードイベントハンドラー
  const handleKeyDown = React.useCallback((event: React.KeyboardEvent, itemId: string) => {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      handleItemToggle(itemId);
    }
  }, [handleItemToggle]);
  // TODO:不要になったら消す
  console.log(filteredChatItems);
  return (
    <div className={rootClassName}>
      {/* SP用閉じるボタン */}
      <div className="simple-modal-edge">
        <ModalCardTop
          showBookmark={false}
          onClickClose={handleClose}
        />
      </div>

      {/* PC用閉じるボタン */}
      <div className="simple-modal-close-pc">
        <Button
          className="simple-modal-close-pc-button"
          icon={<CloseIcon />}
          text
          iconOnly
          onClick={handleClose}
        />
      </div>

      <div className="simple-modal-scroll-wrapper">
        <div className="simple-modal-scroll-inner">
          <div className="simple-modal-header">
            <Header content={title} as="h3" className="simple-modal-title" />
          </div>
          <div className="simple-modal-content">
            <p>検索対象を選択できます。</p>

            {/* チャットID入力フィールド */}
            <div className="simple-modal-chat-input">
              <Input
                placeholder="チャットIDを入力"
                value={chatId}
                onChange={handleChatIdChange}
                fluid
              />
            </div>

            {/* チャットアイテム一覧 */}
            <div className="simple-modal-chat-items">
              {isLoading && (
                <div className="simple-modal-loading">
                  <p>チャットとチャネルを読み込み中...</p>
                </div>
              )}
              {error && (
                <div className="simple-modal-error">
                  <p>
                    エラーが発生しました:
                    {error}
                  </p>
                </div>
              )}
              {!isLoading && !error && filteredChatItems.length === 0 && (
                <div className="simple-modal-no-results">
                  <p>該当するチャットまたはチャネルが見つかりませんでした。</p>
                </div>
              )}
              {!isLoading && !error && filteredChatItems.map((item) => {
                const isSelected = selectedItems.has(item.id);
                const itemClassName = `simple-modal-chat-item${isSelected ? ' selected' : ''}`;
                return (
                  <div
                    key={item.id}
                    className={itemClassName}
                    onClick={() => handleItemToggle(item.id)}
                    onKeyDown={(event) => handleKeyDown(event, item.id)}
                    role="button"
                    tabIndex={0}
                    aria-pressed={isSelected}
                    style={{ cursor: 'pointer' }}
                  >
                    <div className="simple-modal-chat-item-content">
                      <span className="simple-modal-chat-item-label">
                        {item.type}
                        ：
                      </span>
                      <span className="simple-modal-chat-item-name">{item.name}</span>
                    </div>
                    {isSelected ? (
                      <AcceptIcon
                        style={{
                          color: 'var(--color-guide-brand-icon)',
                          fontSize: '20px',
                          transform: 'scale(1.1)',
                          transition: 'transform 0.2s ease, color 0.2s ease',
                        }}
                      />
                    ) : (
                      <AddIcon
                        style={{
                          color: 'var(--color-guide-foreground-2)',
                          fontSize: '20px',
                          transition: 'transform 0.2s ease, color 0.2s ease',
                        }}
                      />
                    )}
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

TeamsSettingModal.defaultProps = {
  className: '',
  open: false,
  title: undefined,
  onClose: undefined,
  // userId: undefined,
  chatItems: undefined, // APIから取得するため、デフォルトは空
};

export default TeamsSettingModal;
