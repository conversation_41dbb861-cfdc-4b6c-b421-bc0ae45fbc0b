import React from 'react';
import { EventReportType, EventReporter } from '@avanade-teams/app-insights-reporter';
import { WeakTokenProvider } from '../../types/TokenProvider';
import { Reporters } from '../../types/Reporters';
import { fetchUrlRes } from '../../utilities/commonFunction';
import { getUniqueNameByToken } from '../../utilities/token/jwt';
import environment from '../../utilities/environment';

// エラー定数
export const UseTeamsChatsApiAccessorError = {
  MISSING_PARAMS: 'MISSING_PARAMS',
  NO_TOKENS: 'NO_TOKENS',
  IS_OFFLINE_OR_SOMETHING_WRONG: 'IS_OFFLINE_OR_SOMETHING_WRONG',
  NO_CONTENT: 'NO_CONTENT',
};

// API URL プレフィックス
const PREFIX = environment.REACT_APP_API_URL ?? '/api';

// Teams チャット設定の型定義
export interface ITeamsChatsRequest {
  chatId?: string;
  channelId?: string;
  chatType: 'チャット' | 'チャネル';
  name: string;
}

export interface ITeamsChatsResponse {
  id: string;
  chatId?: string;
  channelId?: string;
  chatType: string;
  name: string;
  userId: string;
  createdAt: string;
  updatedAt: string;
}

// API 関数の型定義
export type PostTeamsChatsApi = (request: ITeamsChatsRequest) => Promise<void>;
export type GetTeamsChatsApi = () => Promise<ITeamsChatsResponse[]>;
export type DeleteTeamsChatsApi = (id: string) => Promise<void>;

export type UseTeamsChatsApiReturnType = {
  postTeamsChatsApi: PostTeamsChatsApi | undefined;
  getTeamsChatsApi: GetTeamsChatsApi | undefined;
  deleteTeamsChatsApi: DeleteTeamsChatsApi | undefined;
};

/**
 * Teams チャット設定の POST URL を作成する
 * @param userId
 */
export function createPostTeamsChatsUrl(userId: string): string {
  if (!userId) return '';
  return `${PREFIX}/users/${userId}/teams-chats`;
}

/**
 * Teams チャット設定の GET URL を作成する
 * @param userId
 */
export function createGetTeamsChatsUrl(userId: string): string {
  if (!userId) return '';
  return `${PREFIX}/users/${userId}/teams-chats`;
}

/**
 * Teams チャット設定の DELETE URL を作成する
 * @param userId
 * @param id
 */
export function createDeleteTeamsChatsUrl(userId: string, id: string): string {
  if (!userId || !id) return '';
  return `${PREFIX}/users/${userId}/teams-chats/${id}`;
}

/**
 * Teams チャット設定を登録する API 実装
 */
async function postTeamsChatsApiImpl(
  tokenProvider: WeakTokenProvider,
  request: ITeamsChatsRequest,
  report: EventReporter,
): Promise<void> {
  if (!tokenProvider) return Promise.reject(new Error(UseTeamsChatsApiAccessorError.NO_TOKENS));

  // パラメータ検証
  if (!request.name || !request.chatType) {
    return Promise.reject(new Error(UseTeamsChatsApiAccessorError.MISSING_PARAMS));
  }

  if (request.chatType === 'チャット' && !request.chatId) {
    return Promise.reject(new Error(UseTeamsChatsApiAccessorError.MISSING_PARAMS));
  }

  if (request.chatType === 'チャネル' && !request.channelId) {
    return Promise.reject(new Error(UseTeamsChatsApiAccessorError.MISSING_PARAMS));
  }

  const [token, uId] = await getUniqueNameByToken(tokenProvider);

  try {
    const res = await fetchUrlRes(
      token,
      'POST',
      createPostTeamsChatsUrl(uId),
      JSON.stringify(request),
    );

    if (res.status !== 201 && res.status !== 200) {
      report({
        type: EventReportType.SYS_ERROR,
        name: UseTeamsChatsApiAccessorError.IS_OFFLINE_OR_SOMETHING_WRONG,
        error: new Error(res.statusText),
      });
      throw new Error(`API Error: ${res.status} ${res.statusText}`);
    }
  } catch (e) {
    report({
      type: EventReportType.SYS_ERROR,
      name: UseTeamsChatsApiAccessorError.IS_OFFLINE_OR_SOMETHING_WRONG,
      error: e as Error,
    });
    throw e;
  }

  return Promise.resolve();
}

/**
 * Teams チャット設定を取得する API 実装
 */
async function getTeamsChatsApiImpl(
  tokenProvider: WeakTokenProvider,
  report: EventReporter,
): Promise<ITeamsChatsResponse[]> {
  if (!tokenProvider) return Promise.reject(new Error(UseTeamsChatsApiAccessorError.NO_TOKENS));

  const [token, uId] = await getUniqueNameByToken(tokenProvider);

  try {
    const result = await fetchUrlRes(token, 'GET', createGetTeamsChatsUrl(uId));

    if (result.status === 204) {
      report({
        type: EventReportType.SYS_EVENT,
        name: UseTeamsChatsApiAccessorError.NO_CONTENT,
        customProperties: {
          statusText: result.statusText,
        },
      });
      return Promise.resolve([]);
    }

    if (result.status !== 200) {
      report({
        type: EventReportType.SYS_ERROR,
        name: UseTeamsChatsApiAccessorError.IS_OFFLINE_OR_SOMETHING_WRONG,
        error: new Error(result.statusText),
      });
      throw new Error(`API Error: ${result.status} ${result.statusText}`);
    }

    const json = await result.json();
    return Promise.resolve(json);
  } catch (e) {
    report({
      type: EventReportType.SYS_ERROR,
      name: UseTeamsChatsApiAccessorError.IS_OFFLINE_OR_SOMETHING_WRONG,
      error: e as Error,
    });
    throw e;
  }
}

/**
 * Teams チャット設定を削除する API 実装
 */
async function deleteTeamsChatsApiImpl(
  tokenProvider: WeakTokenProvider,
  id: string,
  report: EventReporter,
): Promise<void> {
  if (!tokenProvider) return Promise.reject(new Error(UseTeamsChatsApiAccessorError.NO_TOKENS));
  if (!id) return Promise.reject(new Error(UseTeamsChatsApiAccessorError.MISSING_PARAMS));

  const [token, uId] = await getUniqueNameByToken(tokenProvider);

  try {
    const res = await fetchUrlRes(
      token,
      'DELETE',
      createDeleteTeamsChatsUrl(uId, id),
    );

    if (res.status !== 204 && res.status !== 200) {
      report({
        type: EventReportType.SYS_ERROR,
        name: UseTeamsChatsApiAccessorError.IS_OFFLINE_OR_SOMETHING_WRONG,
        error: new Error(res.statusText),
      });
      throw new Error(`API Error: ${res.status} ${res.statusText}`);
    }
  } catch (e) {
    report({
      type: EventReportType.SYS_ERROR,
      name: UseTeamsChatsApiAccessorError.IS_OFFLINE_OR_SOMETHING_WRONG,
      error: e as Error,
    });
    throw e;
  }

  return Promise.resolve();
}

/**
 * Teams チャット設定 API accessor hook
 * @param tokenProvider
 * @param reporters
 */
const useTeamsChatsApiAccessor = (
  tokenProvider: WeakTokenProvider,
  reporters: Reporters,
): UseTeamsChatsApiReturnType => {
  const [report] = reporters;

  /**
   * Teams チャット設定を登録
   */
  const postTeamsChatsApi: PostTeamsChatsApi = React.useCallback(
    async (request: ITeamsChatsRequest) => postTeamsChatsApiImpl(
      tokenProvider, request, report,
    ), [tokenProvider, report],
  );

  /**
   * Teams チャット設定を取得
   */
  const getTeamsChatsApi: GetTeamsChatsApi = React.useCallback(
    async () => getTeamsChatsApiImpl(
      tokenProvider, report,
    ), [tokenProvider, report],
  );

  /**
   * Teams チャット設定を削除
   */
  const deleteTeamsChatsApi: DeleteTeamsChatsApi = React.useCallback(
    async (id: string) => deleteTeamsChatsApiImpl(
      tokenProvider, id, report,
    ), [tokenProvider, report],
  );

  return {
    postTeamsChatsApi: tokenProvider ? postTeamsChatsApi : undefined,
    getTeamsChatsApi: tokenProvider ? getTeamsChatsApi : undefined,
    deleteTeamsChatsApi: tokenProvider ? deleteTeamsChatsApi : undefined,
  };
};

export default useTeamsChatsApiAccessor;
